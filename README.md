# 终端进度条 Demo (Magefile 版本)

这是一个使用 `github.com/cheggaaa/pb/v3` 库和 `magefile` 构建工具的 Go 终端进度条演示程序。

## 功能特性

该 demo 展示了 5 种不同类型的进度条：

### 1. 基础进度条 (`mage demo:basic`)
- 最简单的进度条实现
- 显示进度百分比和处理速度

### 2. 自定义模板进度条 (`mage demo:template`)
- 使用自定义模板格式
- 支持颜色显示（红色文本）
- 显示预计剩余时间 (ETA)

### 3. 简单并发进度条 (`mage demo:multiple`)
- 演示多个任务的顺序执行
- 每个任务有不同的总数和处理速度
- 显示任务名称前缀

### 4. 文件下载模拟 (`mage demo:download`)
- 模拟文件下载场景
- 显示下载进度和文件大小信息
- 以 MB 为单位显示已下载/总大小

### 5. 带速度显示的进度条 (`mage demo:speed`)
- 显示处理速度变化
- 显示预计剩余时间
- 模拟不同阶段的处理速度变化

## 安装和运行

### 1. 安装 Mage
```bash
go install github.com/magefile/mage@latest
```

### 2. 安装项目依赖
```bash
go mod download
```

### 3. 查看可用命令
```bash
mage -l
```

### 4. 运行示例

#### 使用 Mage 命令（推荐）
```bash
# 运行所有示例
mage demo:all

# 运行单个示例
mage demo:basic      # 基础进度条
mage demo:template   # 自定义模板进度条
mage demo:multiple   # 多任务进度条
mage demo:download   # 文件下载模拟
mage demo:speed      # 带速度显示的进度条

# 显示帮助信息
mage help
```

#### 使用 Make 命令（备选）
```bash
# 显示帮助
make help

# 运行所有示例
make all

# 运行单个示例
make basic      # 基础进度条
make template   # 自定义模板进度条
make multiple   # 多任务进度条
make download   # 文件下载模拟
make speed      # 带速度显示的进度条

# 安装依赖
make install
```

## 代码结构

- `magefile.go` - Mage 构建文件，包含所有 demo 命令
- `main.go` - 原始主程序文件（保留作为参考）
- `Demo` namespace - 包含所有进度条示例的命令组
  - `Demo.All()` - 运行所有示例
  - `Demo.Basic()` - 基础进度条示例
  - `Demo.Template()` - 自定义模板进度条
  - `Demo.Multiple()` - 多任务进度条
  - `Demo.Download()` - 文件下载模拟
  - `Demo.Speed()` - 带速度显示的进度条

## 依赖

- Go 1.16+
- github.com/cheggaaa/pb/v3
- github.com/magefile/mage

## Mage 命令详解

### 查看所有可用命令
```bash
mage -l
```

### 运行特定命令
```bash
mage demo:basic    # 运行基础进度条示例
mage demo:all      # 运行所有示例
mage help          # 显示帮助信息
```

### 命令参数
Mage 支持命令行参数和环境变量，可以通过以下方式自定义行为：
```bash
# 使用详细输出
mage -v demo:all

# 显示执行时间
mage -t demo:basic
```

## 特性说明

### 进度条元素
- `[=====>___________]` - 进度条视觉表示
- `45.00%` - 完成百分比
- `20 p/s` - 每秒处理项目数
- `ETA: 5s` - 预计剩余时间

### 自定义选项
- 前缀文本：`bar.Set("prefix", "任务名: ")`
- 后缀文本：`bar.Set("suffix", " 额外信息")`
- 自定义模板：支持颜色和格式化
- 刷新率：`SetRefreshRate(time.Millisecond * 10)`

## 优势

使用 Magefile 的优势：
- ✅ **模块化命令** - 每个示例都是独立的命令
- ✅ **易于扩展** - 添加新示例只需要添加新方法
- ✅ **命令行友好** - 支持 tab 补全和帮助信息
- ✅ **跨平台** - 在所有支持 Go 的平台上运行
- ✅ **无需额外配置** - 不需要 Makefile 或 shell 脚本

这个 demo 展示了 pb/v3 库的主要功能，并演示了如何使用 Mage 构建工具来组织和管理命令行工具。
