# 终端进度条 Demo

这是一个使用 `github.com/cheggaaa/pb/v3` 库的 Go 终端进度条演示程序。

## 功能特性

该 demo 展示了 5 种不同类型的进度条：

### 1. 基础进度条
- 最简单的进度条实现
- 显示进度百分比和处理速度

### 2. 自定义模板进度条
- 使用自定义模板格式
- 支持颜色显示（红色文本）
- 显示预计剩余时间 (ETA)

### 3. 简单并发进度条
- 演示多个任务的顺序执行
- 每个任务有不同的总数和处理速度
- 显示任务名称前缀

### 4. 文件下载模拟
- 模拟文件下载场景
- 显示下载进度和文件大小信息
- 以 MB 为单位显示已下载/总大小

### 5. 带速度显示的进度条
- 显示处理速度变化
- 显示预计剩余时间
- 模拟不同阶段的处理速度变化

## 运行方法

```bash
# 初始化项目（如果还没有 go.mod）
go mod init progress-bar-demo

# 安装依赖
go get github.com/cheggaaa/pb/v3

# 运行 demo
go run main.go
```

## 代码结构

- `main.go` - 主程序文件，包含所有 demo 函数
- `basicProgressBar()` - 基础进度条示例
- `customTemplateProgressBar()` - 自定义模板进度条
- `simpleMultipleProgressBars()` - 多任务进度条
- `fileDownloadSimulation()` - 文件下载模拟
- `speedProgressBar()` - 带速度显示的进度条

## 依赖

- Go 1.16+
- github.com/cheggaaa/pb/v3

## 特性说明

### 进度条元素
- `[=====>___________]` - 进度条视觉表示
- `45.00%` - 完成百分比
- `20 p/s` - 每秒处理项目数
- `ETA: 5s` - 预计剩余时间

### 自定义选项
- 前缀文本：`bar.Set("prefix", "任务名: ")`
- 后缀文本：`bar.Set("suffix", " 额外信息")`
- 自定义模板：支持颜色和格式化
- 刷新率：`SetRefreshRate(time.Millisecond * 10)`

这个 demo 展示了 pb/v3 库的主要功能，适合学习和参考使用。
