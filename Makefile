.PHONY: help install all basic template multiple download speed clean

# Default target
help:
	@echo "进度条 Demo - 可用命令:"
	@echo ""
	@echo "  make install   - 安装 mage 和项目依赖"
	@echo "  make all       - 运行所有进度条示例"
	@echo "  make basic     - 基础进度条示例"
	@echo "  make template  - 自定义模板进度条示例"
	@echo "  make multiple  - 多任务进度条示例"
	@echo "  make download  - 文件下载模拟示例"
	@echo "  make speed     - 带速度显示的进度条示例"
	@echo "  make clean     - 清理构建文件"
	@echo "  make help      - 显示此帮助信息"
	@echo ""
	@echo "注意: 这些命令实际上调用的是 mage 命令"

# Install mage and dependencies
install:
	@echo "安装 mage..."
	go install github.com/magefile/mage@latest
	@echo "安装项目依赖..."
	go mod download
	@echo "安装完成!"

# Run all demos
all:
	mage demo:all

# Individual demo commands
basic:
	mage demo:basic

template:
	mage demo:template

multiple:
	mage demo:multiple

download:
	mage demo:download

speed:
	mage demo:speed

# Clean build artifacts
clean:
	go clean
	@echo "清理完成!"

# Show mage targets
list:
	mage -l
