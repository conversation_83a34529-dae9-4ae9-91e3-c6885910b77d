//go:build mage

package main

import (
	"fmt"
	"strings"
	"time"

	"github.com/cheggaaa/pb/v3"
	"github.com/magefile/mage/mg"
)

// Demo namespace for progress bar demonstrations
type Demo mg.Namespace

// All runs all progress bar demos
func (Demo) All() error {
	fmt.Println("=== 终端进度条 Demo ===\n")

	fmt.Println("1. 基础进度条:")
	if err := (Demo{}).Basic(); err != nil {
		return err
	}

	fmt.Println("\n" + strings.Repeat("=", 50) + "\n")

	fmt.Println("2. 自定义模板进度条:")
	if err := (Demo{}).Template(); err != nil {
		return err
	}

	fmt.Println("\n" + strings.Repeat("=", 50) + "\n")

	fmt.Println("3. 简单并发进度条:")
	if err := (Demo{}).Multiple(); err != nil {
		return err
	}

	fmt.Println("\n" + strings.Repeat("=", 50) + "\n")

	fmt.Println("4. 文件下载模拟:")
	if err := (Demo{}).Download(); err != nil {
		return err
	}

	fmt.Println("\n" + strings.Repeat("=", 50) + "\n")

	fmt.Println("5. 带速度显示的进度条:")
	if err := (Demo{}).Speed(); err != nil {
		return err
	}

	fmt.Println("\n所有 Demo 完成!")
	return nil
}

// Basic demonstrates a basic progress bar
func (Demo) Basic() error {
	fmt.Println("运行基础进度条示例...")
	count := 100
	bar := pb.StartNew(count)

	for i := 0; i < count; i++ {
		bar.Increment()
		time.Sleep(50 * time.Millisecond)
	}

	bar.Finish()
	return nil
}

// Template demonstrates a custom template progress bar
func (Demo) Template() error {
	fmt.Println("运行自定义模板进度条示例...")
	count := 100

	// 创建自定义模板
	tmpl := `{{ red "处理中..." }} {{bar . }} {{percent .}} {{speed . }} {{rtime . "ETA %s"}}`

	bar := pb.ProgressBarTemplate(tmpl).Start(count)

	for i := 0; i < count; i++ {
		bar.Increment()
		time.Sleep(30 * time.Millisecond)
	}

	bar.Finish()
	return nil
}

// Multiple demonstrates multiple progress bars running sequentially
func (Demo) Multiple() error {
	fmt.Println("运行多任务进度条示例...")

	fmt.Println("任务1: 处理文件...")
	task1 := pb.StartNew(50)
	task1.Set("prefix", "任务1: ")

	for i := 0; i < 50; i++ {
		task1.Increment()
		time.Sleep(100 * time.Millisecond)
	}
	task1.Finish()

	fmt.Println("任务2: 数据转换...")
	task2 := pb.StartNew(100)
	task2.Set("prefix", "任务2: ")

	for i := 0; i < 100; i++ {
		task2.Increment()
		time.Sleep(50 * time.Millisecond)
	}
	task2.Finish()

	fmt.Println("任务3: 结果验证...")
	task3 := pb.StartNew(75)
	task3.Set("prefix", "任务3: ")

	for i := 0; i < 75; i++ {
		task3.Increment()
		time.Sleep(80 * time.Millisecond)
	}
	task3.Finish()

	return nil
}

// Download simulates a file download with progress bar
func (Demo) Download() error {
	fmt.Println("运行文件下载模拟示例...")
	fileSize := int64(1024 * 1024 * 10)         // 10MB
	totalChunks := int(fileSize / (1024 * 100)) // 100KB chunks

	bar := pb.StartNew(totalChunks)
	bar.Set("prefix", "下载文件 (10MB): ")

	downloaded := int64(0)
	chunkSize := int64(1024 * 100) // 100KB chunks

	for i := 0; i < totalChunks; i++ {
		// 模拟网络延迟
		time.Sleep(20 * time.Millisecond)

		downloaded += chunkSize
		bar.Increment()

		// 显示下载进度信息
		if i%10 == 0 {
			mb := float64(downloaded) / (1024 * 1024)
			bar.Set("suffix", fmt.Sprintf(" %.1f/10.0 MB", mb))
		}
	}

	bar.Finish()
	return nil
}

// Speed demonstrates a progress bar with variable speed
func (Demo) Speed() error {
	fmt.Println("运行带速度显示的进度条示例...")
	count := 200

	// 使用内置的全功能模板
	tmpl := `{{string . "prefix"}}{{bar . }} {{percent .}} {{speed . }} {{rtime . "ETA: %s"}}`

	bar := pb.ProgressBarTemplate(tmpl).Start(count)
	bar.Set("prefix", "数据处理: ")

	for i := 0; i < count; i++ {
		bar.Increment()
		// 模拟不同的处理速度
		if i < 50 {
			time.Sleep(100 * time.Millisecond) // 慢速开始
		} else if i < 150 {
			time.Sleep(20 * time.Millisecond) // 加速
		} else {
			time.Sleep(80 * time.Millisecond) // 减速结束
		}
	}

	bar.Finish()
	return nil
}

// Help displays available commands
func Help() error {
	fmt.Println("可用的进度条 Demo 命令:")
	fmt.Println("")
	fmt.Println("  mage demo:all      - 运行所有进度条示例")
	fmt.Println("  mage demo:basic    - 基础进度条示例")
	fmt.Println("  mage demo:template - 自定义模板进度条示例")
	fmt.Println("  mage demo:multiple - 多任务进度条示例")
	fmt.Println("  mage demo:download - 文件下载模拟示例")
	fmt.Println("  mage demo:speed    - 带速度显示的进度条示例")
	fmt.Println("  mage help          - 显示此帮助信息")
	fmt.Println("")
	fmt.Println("示例:")
	fmt.Println("  mage demo:all      # 运行所有示例")
	fmt.Println("  mage demo:basic    # 只运行基础进度条")
	return nil
}

// Default target runs help
var Default = Help
