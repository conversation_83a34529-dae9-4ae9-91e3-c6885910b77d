package aigc

const GiteeAI = "https://ai.gitee.com/v1"
const Authorization = "Bearer 6I8WAFWMAGNJBCDMEJ8FQMCCJVQR8BDNISC5IM1C"
const ImageAPI = "/images/generations"
const Model = "FLUX.1-Krea-dev"

type PaintingResultResponse struct {
	Created int `json:"created"`
	Data    []struct {
		Url string `json:"url"`
	} `json:"data"`
}

type Painting struct {
	Prompt              string `json:"prompt"`
	NegativePrompt      string `json:"negative_prompt"`
	GuidanceScale       int    `json:"guidance_scale"`
	NumInferenceSteps   int    `json:"num_inference_steps"`
	Width               int    `json:"width"`
	Height              int    `json:"height"`
	NumImagesPerPrompt  int    `json:"num_images_per_prompt"`
	Strength            int    `json:"strength"`
}

	body := map[string]any{
		"model":               "flux-1-schnell",
		"prompt":              painting.Prompt,
		"negative_prompt":     painting.NegativePrompt,
		"guidance_scale":      painting.GuidanceScale,
		"num_inference_steps": painting.NumInferenceSteps,
		"steps":               painting.NumInferenceSteps,
		"size":                fmt.Sprintf("%dx%d", painting.Width, painting.Height),
		"n":                   1,
		"response_format":     "url",
	}